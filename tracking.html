<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Your Package - VersaTradez | Real-time Package Tracking</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="alternate icon" href="assets/logo.jpg">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Clean Professional Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container">
            <!-- Brand Logo -->
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <img src="assets/logo.jpg" alt="VersaTradez Logo" width="40" height="40" class="me-2 logo-img">
                <span class="fw-bold fs-4 brand-text">VersaTradez</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler border-0 shadow-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation -->
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link fw-medium px-3" href="index.html">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle fw-medium px-3" href="#" role="button" data-bs-toggle="dropdown">
                            Services
                        </a>
                        <ul class="dropdown-menu shadow border-0 mt-2">
                            <li><a class="dropdown-item py-2" href="services.html">Express Delivery</a></li>
                            <li><a class="dropdown-item py-2" href="services.html">Standard Shipping</a></li>
                            <li><a class="dropdown-item py-2" href="services.html">International</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item py-2" href="business.html">Business Solutions</a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link active fw-medium px-3" href="tracking.html">Track Package</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium px-3" href="about.html">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium px-3" href="support.html">Support</a>
                    </li>
                </ul>

                <!-- Right Side Actions -->
                <div class="d-flex align-items-center">
                    <a href="contact-form.html" class="btn btn-primary me-2 px-4">
                        Get Quote
                    </a>
                    <a href="tracking.html" class="btn btn-outline-primary px-4">
                        Track Package
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Tracking Hero Section -->
    <section class="tracking-hero">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item active">Track Package</li>
                        </ol>
                    </nav>
                    <h1 class="tracking-title">Track Your Package</h1>
                    <p class="tracking-subtitle">Enter your tracking number below to get real-time updates on your shipment's location and delivery status</p>
                    
                    <!-- Enhanced Tracking Form -->
                    <div class="tracking-form-container">
                        <form id="trackingForm" class="enhanced-tracking-form">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="trackingNumber" placeholder="Enter tracking number (e.g., VT001234567)" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search me-2"></i>Track Package
                                </button>
                            </div>
                        </form>
                        
                        <!-- Quick Track Buttons -->
                        <div class="quick-track-buttons mt-3">
                            <p class="text-muted mb-2">Try these sample tracking numbers:</p>
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="quickTrack('VT001234567')">VT001234567</button>
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="quickTrack('VT001234568')">VT001234568</button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="quickTrack('VT001234570')">VT001234570</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking Results -->
    <section class="tracking-results py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div id="trackingResult" class="tracking-result" style="display: none;">
                        <!-- Tracking results will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking Features -->
    <section class="tracking-features py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Advanced Tracking Features</h2>
                <p class="section-subtitle">Experience the most comprehensive package tracking in Egypt</p>
            </div>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5>Real-time GPS Tracking</h5>
                        <p>See your package's exact location on an interactive map with live updates every 15 minutes</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h5>Smart Notifications</h5>
                        <p>Get instant SMS, email, and WhatsApp alerts for every status change and delivery attempt</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h5>Delivery Time Prediction</h5>
                        <p>AI-powered delivery time estimates with 95% accuracy based on real traffic and weather data</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="script.js"></script>
    
    <script>
        function quickTrack(trackingNumber) {
            document.getElementById('trackingNumber').value = trackingNumber;
            document.getElementById('trackingForm').dispatchEvent(new Event('submit'));
        }
    </script>
</body>
</html>
